<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>名言卡片点击功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #28a745;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #218838;
        }
        .test-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        /* 模拟名言卡片样式 */
        .quote-card-component {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            background: white;
            transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease;
        }
        
        .quote-card-component.cursor-pointer {
            cursor: pointer;
        }
        
        .quote-card-component.cursor-pointer:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
            border-color: #ffd300;
        }
        
        .quote-content {
            font-size: 18px;
            font-style: italic;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .quote-author {
            font-weight: bold;
            color: #666;
            margin-bottom: 10px;
        }
        
        .click-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .quote-card-component:hover .click-indicator {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🖱️ 名言卡片点击功能测试</h1>
        <p>本页面专门测试名言卡片的点击跳转功能是否正常工作。</p>
        
        <div style="display: flex; gap: 10px; margin-bottom: 20px;">
            <button class="test-button" onclick="testClickFunctionality()">🧪 测试点击功能</button>
            <button class="test-button" onclick="createTestCards()">📋 创建测试卡片</button>
            <button class="test-button" onclick="clearResults()">🧹 清除结果</button>
        </div>

        <div id="test-status" class="test-result info">
            准备测试名言卡片点击功能...
        </div>
    </div>

    <!-- 测试卡片容器 -->
    <div class="test-container">
        <h2 class="test-title">测试卡片</h2>
        <p>点击下面的卡片测试跳转功能：</p>
        <div id="test-cards-container"></div>
        <div id="click-results" class="test-result"></div>
    </div>

    <!-- 实际首页卡片测试 -->
    <div class="test-container">
        <h2 class="test-title">首页卡片测试</h2>
        <button class="test-button" onclick="loadHomepageCards()">📄 加载首页卡片</button>
        <div id="homepage-cards-container"></div>
        <div id="homepage-results" class="test-result"></div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/config.js"></script>
    <script src="js/mock-data.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/components/quote-card.js"></script>

    <script>
        // 工具函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                element.className = `test-result ${type}`;
            }
        }

        // 测试点击功能
        function testClickFunctionality() {
            showResult('test-status', '正在测试点击功能...', 'info');
            
            try {
                // 检查必要的对象是否存在
                const checks = {
                    'UrlHandler': typeof UrlHandler !== 'undefined',
                    'getQuoteUrl方法': typeof UrlHandler?.getQuoteUrl === 'function',
                    'QuoteCardComponent': typeof QuoteCardComponent !== 'undefined',
                    'ApiClient': typeof window.ApiClient !== 'undefined',
                    'getQuote方法': typeof window.ApiClient?.getQuote === 'function'
                };

                let allPassed = true;
                let resultText = '点击功能检查结果:\n\n';

                for (const [name, passed] of Object.entries(checks)) {
                    resultText += `${passed ? '✅' : '❌'} ${name}: ${passed ? '可用' : '不可用'}\n`;
                    if (!passed) allPassed = false;
                }

                if (allPassed) {
                    // 测试URL生成
                    const testQuote = { id: 1, content: "Test quote" };
                    const url = UrlHandler.getQuoteUrl(testQuote);
                    
                    resultText += `\n✅ URL生成测试: ${url}\n`;
                    resultText += `✅ 所有功能检查通过！点击功能应该正常工作。`;
                    
                    showResult('test-status', resultText, 'success');
                } else {
                    resultText += `\n❌ 发现问题，点击功能可能无法正常工作。`;
                    showResult('test-status', resultText, 'error');
                }

            } catch (error) {
                showResult('test-status', `点击功能测试失败: ${error.message}`, 'error');
            }
        }

        // 创建测试卡片
        function createTestCards() {
            showResult('click-results', '正在创建测试卡片...', 'info');
            
            try {
                const container = document.getElementById('test-cards-container');
                container.innerHTML = '';

                // 测试数据
                const testQuotes = [
                    {
                        id: 1,
                        content: "Imagination is more important than knowledge.",
                        author: { id: 1, name: "Albert Einstein" },
                        categories: [{ id: 1, name: "Science" }],
                        sources: [{ id: 1, name: "Interview" }]
                    },
                    {
                        id: 2,
                        content: "The way to get started is to quit talking and begin doing.",
                        author: { id: 2, name: "Walt Disney" },
                        categories: [{ id: 2, name: "Motivation" }],
                        sources: [{ id: 2, name: "Speech" }]
                    },
                    {
                        id: 3,
                        content: "Innovation distinguishes between a leader and a follower.",
                        author: { id: 3, name: "Steve Jobs" },
                        categories: [{ id: 3, name: "Leadership" }],
                        sources: [{ id: 3, name: "Keynote" }]
                    }
                ];

                // 使用QuoteCardComponent创建卡片
                testQuotes.forEach((quote, index) => {
                    const quoteCard = QuoteCardComponent.render(quote, index);
                    
                    // 添加点击测试指示器
                    const indicator = document.createElement('div');
                    indicator.className = 'click-indicator';
                    indicator.textContent = '点击测试';
                    quoteCard.style.position = 'relative';
                    quoteCard.appendChild(indicator);
                    
                    // 添加测试点击事件监听
                    quoteCard.addEventListener('click', (e) => {
                        if (!e.target.closest('button') && !e.target.closest('a')) {
                            const url = UrlHandler.getQuoteUrl(quote);
                            showResult('click-results', 
                                `✅ 卡片点击测试成功!\n` +
                                `名言ID: ${quote.id}\n` +
                                `作者: ${quote.author.name}\n` +
                                `生成URL: ${url}\n` +
                                `点击时间: ${new Date().toLocaleTimeString()}\n\n` +
                                `注意: 这是测试模式，实际不会跳转。\n` +
                                `要测试真实跳转，请点击下面的"打开详情页"按钮。`, 'success');
                            
                            // 高亮点击的卡片
                            quoteCard.style.borderColor = '#28a745';
                            quoteCard.style.borderWidth = '2px';
                            setTimeout(() => {
                                quoteCard.style.borderColor = '';
                                quoteCard.style.borderWidth = '';
                            }, 2000);
                            
                            // 添加打开详情页按钮
                            setTimeout(() => {
                                const openButton = document.createElement('button');
                                openButton.textContent = `🔗 打开名言 ${quote.id} 详情页`;
                                openButton.className = 'test-button';
                                openButton.onclick = () => window.open(url, '_blank');
                                
                                const resultElement = document.getElementById('click-results');
                                resultElement.appendChild(document.createElement('br'));
                                resultElement.appendChild(openButton);
                            }, 500);
                        }
                    });
                    
                    container.appendChild(quoteCard);
                });

                showResult('click-results', `✅ 已创建 ${testQuotes.length} 个测试卡片，点击任意卡片进行测试。`, 'success');

            } catch (error) {
                showResult('click-results', `创建测试卡片失败: ${error.message}`, 'error');
            }
        }

        // 加载首页卡片
        async function loadHomepageCards() {
            showResult('homepage-results', '正在加载首页卡片...', 'info');
            
            try {
                // 获取真实的名言数据
                const quotes = await window.ApiClient.getQuotes(1, 3);
                
                if (quotes && quotes.quotes && quotes.quotes.length > 0) {
                    const container = document.getElementById('homepage-cards-container');
                    container.innerHTML = '';
                    
                    // 使用真实数据创建卡片
                    quotes.quotes.forEach((quote, index) => {
                        const quoteCard = QuoteCardComponent.render(quote, index);
                        container.appendChild(quoteCard);
                    });
                    
                    showResult('homepage-results', 
                        `✅ 已加载 ${quotes.quotes.length} 个真实的名言卡片\n` +
                        `这些卡片使用真实的API数据，点击应该能正常跳转到详情页。\n` +
                        `请点击任意卡片测试跳转功能。`, 'success');
                } else {
                    showResult('homepage-results', '❌ 无法获取名言数据', 'error');
                }

            } catch (error) {
                showResult('homepage-results', `加载首页卡片失败: ${error.message}`, 'error');
            }
        }

        function clearResults() {
            ['test-status', 'click-results', 'homepage-results'].forEach(id => {
                showResult(id, '', 'info');
            });
            
            document.getElementById('test-cards-container').innerHTML = '';
            document.getElementById('homepage-cards-container').innerHTML = '';
            
            showResult('test-status', '结果已清除，准备重新测试...', 'info');
        }

        // 页面加载时初始化
        window.addEventListener('load', function() {
            console.log('名言卡片点击功能测试页面已加载');
            showResult('test-status', 
                '🖱️ 点击功能测试工具已准备就绪!\n\n' +
                '1. 点击"测试点击功能"检查基础功能\n' +
                '2. 点击"创建测试卡片"生成测试卡片\n' +
                '3. 点击"加载首页卡片"测试真实数据\n' +
                '4. 点击任意名言卡片测试跳转功能', 'info');
        });
    </script>
</body>
</html>
